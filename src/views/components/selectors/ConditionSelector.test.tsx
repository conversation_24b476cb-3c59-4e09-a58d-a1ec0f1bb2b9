import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { Condition, ConditionKey } from '../../../models/Condition';
import { ConditionSelector } from './ConditionSelector';

type ConditionSelectorProps = React.ComponentProps<typeof ConditionSelector>;
const DEFAULT_PROPS: ConditionSelectorProps = {
  condition: Condition.NEAR_MINT,
  onChange: vi.fn(),
  disabled: false,
  hideTitle: false,
};

function renderConditionSelector(props: Partial<ConditionSelectorProps> = {}) {
  return render(<ConditionSelector {...DEFAULT_PROPS} {...props} />);
}

describe('ConditionSelector', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  describe('with FormLabel', () => {
    it('renders FormLabel', () => {
      renderConditionSelector();
      expect(screen.getByText('Condition', { selector: 'div.form-label--heading' })).toBeInTheDocument();
    });
    it('hides FormLabel when hideTitle is true', () => {
      renderConditionSelector({ hideTitle: true });
      expect(screen.queryByText('Condition', { selector: 'div.form-label--heading' })).not.toBeInTheDocument();
    });
  });
  describe('with IconSelect', () => {
    it('renders all condition options', () => {
      renderConditionSelector();
      Object.keys(Condition).forEach((condition) => {
        expect(screen.getByRole('option', { name: Condition[condition as ConditionKey] })).toBeInTheDocument();
      });
    });
    it("renders 'Multiple' option when condition is 'Multiple'", () => {
      renderConditionSelector({ condition: 'Multiple' });
      const multipleOption = screen.getByRole('option', { name: 'Multiple' });
      expect(multipleOption).toBeInTheDocument();
      expect(multipleOption).toBeDisabled();
    });
    it('calls onChange when selection changes', () => {
      renderConditionSelector();
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: Condition.LIGHTLY_PLAYED } });
      expect(DEFAULT_PROPS.onChange).toHaveBeenCalledWith(Condition.LIGHTLY_PLAYED);
    });
    it('disables the select when disabled prop is true', () => {
      renderConditionSelector({ disabled: true });
      const select = screen.getByRole('combobox');
      expect(select).toBeDisabled();
    });
  });
});
