import { fireEvent, render } from '@testing-library/react';
import * as React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Rarity } from '../../models/Rarity';
import { rarityToChar, SetSymbol, SetSymbolSize } from './SetSymbol';

// Mock the dependencies
vi.mock('../../helpers/core_assets', () => ({
  CoreAssets: {
    iconHost: vi.fn(() => 'https://test-icons.example.com'),
  },
}));

vi.mock('../../helpers/fmt', () => ({
  TextFormat: {
    capitalize: vi.fn((str: string) => str.charAt(0).toUpperCase() + str.slice(1)),
  },
}));

type SetSymbolProps = React.ComponentProps<typeof SetSymbol>;

const DEFAULT_PROPS: SetSymbolProps = {
  setName: 'Test Set',
  setCode: 'TST',
  hoverText: false,
};

function renderSetSymbol(props: Partial<SetSymbolProps> = {}) {
  return render(<SetSymbol {...DEFAULT_PROPS} {...props} />);
}

describe('rarityToChar', () => {
  it('returns undefined for undefined rarity', () => {
    expect(rarityToChar(undefined)).toBeUndefined();
  });

  it('returns "C" for BASIC_LAND', () => {
    expect(rarityToChar(Rarity.BASIC_LAND)).toBe('C');
  });

  it('returns "C" for COMMON', () => {
    expect(rarityToChar(Rarity.COMMON)).toBe('C');
  });

  it('returns "U" for UNCOMMON', () => {
    expect(rarityToChar(Rarity.UNCOMMON)).toBe('U');
  });

  it('returns "R" for RARE', () => {
    expect(rarityToChar(Rarity.RARE)).toBe('R');
  });

  it('returns "M" for MYTHIC_RARE', () => {
    expect(rarityToChar(Rarity.MYTHIC_RARE)).toBe('M');
  });

  it('returns "S" for SPECIAL', () => {
    expect(rarityToChar(Rarity.SPECIAL)).toBe('S');
  });

  it('logs error and returns undefined for invalid rarity', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const invalidRarity = 'invalid' as Rarity;

    const result = rarityToChar(invalidRarity);

    expect(result).toBeUndefined();
    expect(consoleSpy).toHaveBeenCalledWith('Invalid rarity invalid converted to COMMON');

    consoleSpy.mockRestore();
  });
});

describe('SetSymbol', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('component render', () => {
    it('renders with basic props', () => {
      const { container } = renderSetSymbol();

      expect(container.querySelector('.flex')).toBeInTheDocument();
      expect(container.querySelector('.set-symbol__xs')).toBeInTheDocument();
      expect(container.querySelector('img')).toBeInTheDocument();
    });

    it('applies correct size class when size is provided', () => {
      const { container } = renderSetSymbol({ size: SetSymbolSize.XL });

      expect(container.querySelector('.set-symbol__xl')).toBeInTheDocument();
      expect(container.querySelector('.set-symbol__xs')).not.toBeInTheDocument();
    });

    it('defaults to XS size when no size is provided', () => {
      const { container } = renderSetSymbol();

      expect(container.querySelector('.set-symbol__xs')).toBeInTheDocument();
    });

    it('generates correct image source without rarity', () => {
      const { container } = renderSetSymbol({ setCode: 'ABC' });
      const img = container.querySelector('img') as HTMLImageElement;

      expect(img.src).toBe('https://test-icons.example.com/set_symbols/ABC_large.png');
    });

    it('generates correct image source with rarity', () => {
      const { container } = renderSetSymbol({
        setCode: 'ABC',
        rarity: Rarity.RARE,
      });
      const img = container.querySelector('img') as HTMLImageElement;

      expect(img.src).toBe('https://test-icons.example.com/set_symbols/ABC_R_large.png');
    });
  });

  describe('hover functionality', () => {
    it('does not show tooltip when hoverText is false', () => {
      const { container } = renderSetSymbol({ hoverText: false });
      const tooltipContainer = container.querySelector('.set-symbol-tooltip-container');

      expect(tooltipContainer).not.toBeInTheDocument();
    });

    it('does not show tooltip initially when hoverText is true', () => {
      const { container } = renderSetSymbol({ hoverText: true });
      const tooltipContainer = container.querySelector('.set-symbol-tooltip-container');

      expect(tooltipContainer).not.toBeInTheDocument();
    });

    it('shows tooltip on mouse enter when hoverText is true', () => {
      const { container } = renderSetSymbol({
        hoverText: true,
        setName: 'Test Set Name',
        rarity: Rarity.RARE,
        collectorNumber: '123',
      });

      const symbolDiv = container.querySelector('.flex') as HTMLElement;
      fireEvent.mouseEnter(symbolDiv);

      const tooltipContainer = container.querySelector('.set-symbol-tooltip-container');
      expect(tooltipContainer).toBeInTheDocument();

      const tooltip = container.querySelector('.set-symbol-tooltip');
      expect(tooltip).toBeInTheDocument();
      expect(tooltip?.textContent).toContain('Test Set Name');
      expect(tooltip?.textContent).toContain('Rare - 123');
    });

    it('hides tooltip on mouse leave', () => {
      const { container } = renderSetSymbol({ hoverText: true });
      const symbolDiv = container.querySelector('.flex') as HTMLElement;

      // Show tooltip first
      fireEvent.mouseEnter(symbolDiv);
      expect(container.querySelector('.set-symbol-tooltip-container')).toBeInTheDocument();

      // Hide tooltip
      fireEvent.mouseLeave(symbolDiv);
      expect(container.querySelector('.set-symbol-tooltip-container')).not.toBeInTheDocument();
    });

    it('shows tooltip with rarity but no collector number', () => {
      const { container } = renderSetSymbol({
        hoverText: true,
        setName: 'Test Set',
        rarity: Rarity.UNCOMMON,
      });

      const symbolDiv = container.querySelector('.flex') as HTMLElement;
      fireEvent.mouseEnter(symbolDiv);

      const tooltip = container.querySelector('.set-symbol-tooltip');
      expect(tooltip?.textContent).toContain('Test Set');
      expect(tooltip?.textContent).toContain('Uncommon');
      expect(tooltip?.textContent).not.toContain(' - ');
    });

    it('shows tooltip with collector number but no rarity', () => {
      const { container } = renderSetSymbol({
        hoverText: true,
        setName: 'Test Set',
        collectorNumber: '456',
      });

      const symbolDiv = container.querySelector('.flex') as HTMLElement;
      fireEvent.mouseEnter(symbolDiv);

      const tooltip = container.querySelector('.set-symbol-tooltip');
      expect(tooltip?.textContent).toContain('Test Set');
      expect(tooltip?.textContent).toContain(' - 456');
    });
  });

  describe('mouse event handlers', () => {
    it('calls preventDefault on mouse enter', () => {
      const { container } = renderSetSymbol();
      const symbolDiv = container.querySelector('.flex') as HTMLElement;

      const mockEvent = { preventDefault: vi.fn() } as any;
      fireEvent.mouseEnter(symbolDiv, mockEvent);

      // We can't directly test preventDefault was called, but we can test the state change
      expect(container.querySelector('.set-symbol-tooltip-container')).not.toBeInTheDocument();
    });

    it('calls preventDefault on mouse leave', () => {
      const { container } = renderSetSymbol({ hoverText: true });
      const symbolDiv = container.querySelector('.flex') as HTMLElement;

      // First enter to set hover state
      fireEvent.mouseEnter(symbolDiv);

      const mockEvent = { preventDefault: vi.fn() } as any;
      fireEvent.mouseLeave(symbolDiv, mockEvent);

      // State should be reset
      expect(container.querySelector('.set-symbol-tooltip-container')).not.toBeInTheDocument();
    });
  });
});
