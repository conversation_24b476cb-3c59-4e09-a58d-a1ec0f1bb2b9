import * as Immutable from 'immutable';
import { EnumHelper } from '../helpers/enum';
import { Game } from './Game';
import { Record } from './Records';

export enum Method {
  IMPORT = 'import',
  EXPORT = 'export',
  EXPORT_STAGED = 'exportStaged',
}

export enum Format {
  CSV = 'csv',
  TEXT = 'txt',
}

// Order of enum values is the order in which they appear as per DataService.sort()
export enum DataServiceType {
  CardCastle = 'cardcastle',
  TCGplayerKiosk = 'tcgplayer_kiosk',
  TCGplayerOnline = 'tcgplayer_online',
  BinderPOS = 'binderpos',
  CrystalCommerce = 'crystalcommerce',
  TCGVault = 'tcg_vault',
  DeckBox = 'deckbox',
  MTGWTF = 'mtgwtf',
  EchoMTG = 'echomtg',
  PucaTrade = 'pucatrade',
  Simple = 'simple',
  ManicWrap = 'manic_wrap',
}

interface IMethodInfo {
  readonly formats: Immutable.List<FormatInfo>;
  readonly available: boolean;
  readonly games: Immutable.List<Game>;
}

export class MethodInfo extends Record<IMethodInfo>({
  formats: Immutable.List(),
  available: false,
  games: Immutable.List(),
}) {
  static fromAPI(data: any): MethodInfo {
    return new MethodInfo({
      formats: Immutable.List(data.formats.map((formatInfo: any) => FormatInfo.fromAPI(formatInfo))),
      available: data.available,
      games: Immutable.List(data.games.map((gameValue: string) => EnumHelper.match(Game, gameValue))),
    });
  }
}

interface IFormatInfo {
  readonly type: Format;
  readonly instructions: Immutable.List<string>;
}

export class FormatInfo extends Record<IFormatInfo>({
  type: Format.CSV,
  instructions: Immutable.List(),
}) {
  static fromAPI(data: any): FormatInfo {
    return new FormatInfo({
      type: EnumHelper.match(Format, data.type) as Format,
      instructions: Immutable.List(data.instructions),
    });
  }
}

interface IDataService {
  readonly type: DataServiceType;
  readonly displayName: string;
  readonly iconURL: string;
  readonly import: MethodInfo;
  readonly export: MethodInfo;
  readonly exportStaged: MethodInfo;
}

export class DataService extends Record<IDataService>({
  type: DataServiceType.CardCastle,
  displayName: '',
  iconURL: '',
  import: new MethodInfo(),
  export: new MethodInfo(),
  exportStaged: new MethodInfo(),
}) {
  static fromAPI(data: any): DataService {
    return new DataService({
      type: EnumHelper.match(DataServiceType, data.type) as DataServiceType,
      displayName: data.display_name,
      iconURL: data.icon_url,
      import: MethodInfo.fromAPI(data.import),
      export: MethodInfo.fromAPI(data.export),
      exportStaged: MethodInfo.fromAPI(data.export_staged),
    });
  }

  static sort(services: Immutable.List<DataService>): Immutable.List<DataService> {
    return services
      .sortBy((service: DataService) => EnumHelper.allCases(DataServiceType).indexOf(service.get('type')))
      .toList();
  }

  supports(method?: Method, format?: Format, game?: Game): boolean {
    if (!method || !format || !game) return false;

    const available = this.get(method).get('available');
    const matchedFormat = this.findFormat(method, format);
    const supported = matchedFormat !== undefined && this.get(method).get('games').includes(game);

    return available && supported;
  }

  instructions(method?: Method, format?: Format): Immutable.List<string> {
    if (!method || !format) return Immutable.List();

    const matchedFormat = this.findFormat(method, format);
    if (!matchedFormat) return Immutable.List();

    return matchedFormat.get('instructions');
  }

  findFormat(method?: Method, format?: Format): FormatInfo | undefined {
    if (!method || !format) return undefined;

    return this.get(method)
      .get('formats')
      .find((formatInfo: FormatInfo) => formatInfo.get('type') === format);
  }
}
