stages:
  - build
  - test
  - deploy_staging
  - deploy_production

before_script:
  - export BUILD_TAG="${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHA}"
  - export BUILD_TAG=`echo $BUILD_TAG | sed -e 's/\//-/g'`
  - export SANITIZED_REF=`echo $CI_COMMIT_REF_NAME | sed -e 's/\//-/g'`

build:
  stage: build
  image: docker:stable
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: '/certs'
  services:
    - docker:dind
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
    - docker pull $CI_REGISTRY_IMAGE:$SANITIZED_REF || true
    - |
      docker build . -f Dockerfile \
        --cache-from $CI_REGISTRY_IMAGE:$SANITIZED_REF \
        -t $CI_REGISTRY_IMAGE:$BUILD_TAG \
        -t $CI_REGISTRY_IMAGE:$CI_PIPELINE_ID \
        -t $CI_REGISTRY_IMAGE:$SANITIZED_REF
    - docker push $CI_REGISTRY_IMAGE:$BUILD_TAG
    - docker push $CI_REGISTRY_IMAGE:$SANITIZED_REF
    - docker push $CI_REGISTRY_IMAGE:$CI_PIPELINE_ID

build_test:
  stage: build
  image: docker:stable
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: '/certs'
  services:
    - docker:dind
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
    - docker pull $CI_REGISTRY_IMAGE/test:$SANITIZED_REF || true
    # Build a test image cached from the recently build production image
    - |
      docker build . -f Dockerfile-Test \
        --cache-from $CI_REGISTRY_IMAGE/test:$SANITIZED_REF \
        -t $CI_REGISTRY_IMAGE/test:$CI_PIPELINE_ID \
        -t $CI_REGISTRY_IMAGE/test:$SANITIZED_REF
    - docker push $CI_REGISTRY_IMAGE/test:$CI_PIPELINE_ID
    - docker push $CI_REGISTRY_IMAGE/test:$SANITIZED_REF

test:
  image: $CI_REGISTRY_IMAGE/test:$CI_PIPELINE_ID
  stage: test
  variables:
    APP_HOST: http://cardcastle.test
  script:
    - cd /cardcastle/dist
    - yarn coverage
    - mkdir -p /builds/cardcastle/frontend/coverage
    - cp -f coverage/cobertura-coverage.xml /builds/cardcastle/frontend/coverage/cobertura-coverage.xml
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura  # or jacoco
        path: coverage/cobertura-coverage.xml

lint:
  image: $CI_REGISTRY_IMAGE/test:$CI_PIPELINE_ID
  stage: test
  script:
    - cd /cardcastle/dist
    - yarn lint

deploy_phoenix:
  stage: deploy_staging
  tags:
    - deployment
    - phoenix
  image: registry.gitlab.com/cardcastle/deeplloyd:latest
  script:
    - kubectl_deploy deployment
  environment: phoenix
  only:
    - /^hotfix\/.*/
    - develop
    - master
    - feature/locations

deploy_hydra:
  stage: deploy_production
  tags:
    - deployment
    - hydra
  image: registry.gitlab.com/cardcastle/deeplloyd:latest
  script:
    - kubectl_deploy deployment
  environment: hydra
  only:
    - master
