import { IFake } from './FakeInterface';

export function create<O>(factory: new () => IFake<O>, options?: Record<string, any>): O {
  return new factory().fake(options);
}

export function createArray<O>(factory: new () => IFake<O>, amount: number): O[] {
  let n = 1;
  if (amount && amount > 0 && Number.isSafeInteger(amount)) {
    n = amount;
  }
  const arrayOfO: O[] = [];
  for (let i = 0; i < n; i++) {
    arrayOfO.push(new factory().fake());
  }
  return arrayOfO;
}

export {
  FakeCardInstance,
  FakeCardPanel,
  FakeMTGCardGroup,
  FakeMTGCardPage,
  FakePrinting,
  FakeTag,
} from './FakeCardData';
export { FakeCardList } from './FakeCardList';
export {
  FakeCardSet,
  FakeCardSetFilter,
  fakeCardSetFilterMap,
  fakeCardSetTypeFilterMap,
  FakeSetTypeFilter,
} from './FakeCardSet';
export {
  FakeColorFilter,
  FakeMTGFilter,
  FakePriceFilter,
  FakeRarity,
  FakeSupertype,
  FakeTagFilter,
  fakeTagFilterMap,
  FakeTypeFilters,
  generateNonEmptyRarityFilter,
  generateNonEmptySubtypesFilter,
  generateNonEmptySupertypesFilter,
} from './FakeFilters';
export { FakeSnapshot } from './FakeSnapshot';
export { FakeStackLocation } from './FakeStackLocation';
export {
  FakeCollectionPreferences,
  FakeLocalizationPreferences,
  FakeUser,
  FakeUserAvatar,
  FakeUserPreferences,
} from './FakeUserData';
