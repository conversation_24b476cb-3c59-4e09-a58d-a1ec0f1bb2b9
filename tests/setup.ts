// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import * as superagent from 'superagent';
import { afterEach, vi } from 'vitest';
import 'vitest-dom/extend-expect';

vi.mock('superagent', async (importOriginal) => {
  const actual: typeof superagent = await importOriginal();
  const generalMock = vi.fn(() => ({
    set: vi.fn().mockReturnThis(),
    then: vi.fn().mockReturnThis(),
    end: vi.fn().mockReturnThis(),
    catch: vi.fn().mockReturnThis(),
    finally: vi.fn().mockReturnThis(),
    send: vi.fn().mockReturnThis(),
  }));

  return {
    ...actual,
    get: generalMock,
    post: generalMock,
    patch: generalMock,
    put: generalMock,
    del: generalMock,
  };
});

// Global mock for NProgress
globalThis.NProgress = {
  start: vi.fn().mockReturnThis(),
  done: vi.fn().mockReturnThis(),
  set: vi.fn().mockReturnThis(),
  inc: vi.fn().mockReturnThis(),
  configure: vi.fn().mockReturnThis(),
  isStarted: vi.fn().mockReturnValue(false),
  remove: vi.fn().mockReturnThis(),
  status: null,
  version: '1.0.0',
};

afterEach(() => {
  cleanup();
});
